<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DigitalAce AI Chat - Responsive</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-left">
            <button class="menu-toggle" id="menuToggle">
                <i class="fas fa-bars"></i>
            </button>
            <a href="#" class="logo">
                <i class="fas fa-robot"></i>
                <span>DigitalAce AI</span>
            </a>
        </div>
        <div class="header-controls">
            <button class="theme-toggle" id="themeToggle">
                <i class="fas fa-moon"></i>
            </button>
            
            <!-- Model Select Dropdown -->
            <div class="model-select" id="modelSelect">
                <button class="model-select-btn">
                    <i class="fas fa-microchip"></i>
                    <span id="currentModelName">GPT-4 Turbo</span>
                    <i class="fas fa-caret-down"></i>
                </button>
                <div class="model-dropdown" id="modelDropdown">
                    <div class="model-option-dropdown active" data-model="gpt-4">
                        <i class="fas fa-brain"></i>
                        <span>GPT-4 Turbo</span>
                    </div>
                    <div class="model-option-dropdown" data-model="claude">
                        <i class="fas fa-book"></i>
                        <span>Claude 3 Opus</span>
                    </div>
                    <div class="model-option-dropdown" data-model="gemini">
                        <i class="fas fa-gem"></i>
                        <span>Gemini Pro</span>
                    </div>
                    <div class="model-option-dropdown" data-model="llama">
                        <i class="fas fa-mountain"></i>
                        <span>Llama 3</span>
                    </div>
                </div>
            </div>
            
            <button class="user-btn">
                <i class="fas fa-user-circle"></i>
                <span>John Doe</span>
            </button>
        </div>
    </div>

    <!-- Main Chat Container -->
    <div class="chat-container">
        <!-- Sidebar -->
        <aside class="sidebar" id="sidebar">
            <button class="close-sidebar" id="closeSidebar">
                <i class="fas fa-times"></i>
            </button>
            <div class="model-selector">
                <h3><i class="fas fa-microchip"></i> AI Models</h3>
                <div class="model-options">
                    <div class="model-option active" data-model="gpt-4">
                        <div class="model-icon">
                            <i class="fas fa-brain"></i>
                        </div>
                        <div class="model-info">
                            <div class="model-name">GPT-4 Turbo</div>
                            <div class="model-desc">Most capable model</div>
                        </div>
                    </div>
                    <div class="model-option" data-model="claude">
                        <div class="model-icon">
                            <i class="fas fa-book"></i>
                        </div>
                        <div class="model-info">
                            <div class="model-name">Claude 3 Opus</div>
                            <div class="model-desc">Advanced reasoning</div>
                        </div>
                    </div>
                    <div class="model-option" data-model="gemini">
                        <div class="model-icon">
                            <i class="fas fa-gem"></i>
                        </div>
                        <div class="model-info">
                            <div class="model-name">Gemini Pro</div>
                            <div class="model-desc">Google's AI model</div>
                        </div>
                    </div>
                    <div class="model-option" data-model="llama">
                        <div class="model-icon">
                            <i class="fas fa-mountain"></i>
                        </div>
                        <div class="model-info">
                            <div class="model-name">Llama 3</div>
                            <div class="model-desc">Open source model</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="conversations">
                <h3><i class="fas fa-comments"></i> Conversations</h3>
                <button class="new-chat-btn" id="newChatBtn">
                    <i class="fas fa-plus"></i> New Chat
                </button>
                <ul class="conversation-list">
                    <li class="conversation-item active">
                        <i class="fas fa-comment"></i> Project Planning
                    </li>
                    <li class="conversation-item">
                        <i class="fas fa-comment"></i> Code Debugging
                    </li>
                    <li class="conversation-item">
                        <i class="fas fa-comment"></i> Marketing Strategy
                    </li>
                    <li class="conversation-item">
                        <i class="fas fa-comment"></i> Content Ideas
                    </li>
                    <li class="conversation-item">
                        <i class="fas fa-comment"></i> UI Design Feedback
                    </li>
                </ul>
            </div>
        </aside>

        <!-- Chat Area -->
        <div class="chat-area">
            <div class="chat-header">
                <div class="current-model">
                    <div class="model-indicator"></div>
                    <div>GPT-4 Turbo</div>
                </div>
            </div>
            
            <div class="chat-messages" id="chatMessages">
                <div class="message ai-message">
                    <div class="avatar ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>Hello! I'm your AI assistant. How can I help you today?</p>
                        <p>You can ask me anything - I can help with coding, writing, research, and much more. What would you like to start with?</p>
                        <button class="copy-btn" title="Copy message"><i class="fas fa-copy"></i></button>
                        <div class="message-timestamp">Just now</div>
                    </div>
                </div>
                
                <div class="message user-message">
                    <div class="avatar user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="message-content">
                        <p>Hi! Can you help me design a responsive chatbot interface with dark/light mode toggle?</p>
                        <button class="copy-btn" title="Copy message"><i class="fas fa-copy"></i></button>
                        <div class="message-timestamp">Just now</div>
                    </div>
                </div>
                
                <div class="message ai-message">
                    <div class="avatar ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>Absolutely! I'd be happy to help you design a responsive chatbot interface with theme toggling. Here are the key components we'll need:</p>
                        <ol>
                            <li><strong>Theme System</strong>: CSS variables for both light and dark modes</li>
                            <li><strong>Responsive Layout</strong>: Flexbox/Grid that adapts to mobile, tablet, and desktop</li>
                            <li><strong>Chat Interface</strong>: Message bubbles, input area, and send button</li>
                            <li><strong>Model Selection</strong>: Sidebar to choose between different AI models</li>
                        </ol>
                        <p>Would you like me to generate some code examples or design mockups?</p>
                        <button class="copy-btn" title="Copy message"><i class="fas fa-copy"></i></button>
                        <div class="message-timestamp">Just now</div>
                    </div>
                </div>
                
                <div class="message user-message">
                    <div class="avatar user-avatar">
                        <i class="fas fa-user"></i>
                    </div>
                    <div class="message-content">
                        <p>Yes, please show me a code example for the theme toggle functionality.</p>
                        <button class="copy-btn" title="Copy message"><i class="fas fa-copy"></i></button>
                        <div class="message-timestamp">Just now</div>
                    </div>
                </div>
                
                <div class="message ai-message">
                    <div class="avatar ai-avatar">
                        <i class="fas fa-robot"></i>
                    </div>
                    <div class="message-content">
                        <p>Here's a simple implementation of a theme toggle using JavaScript and CSS variables:</p>
                        <pre><code>// Theme toggle functionality
const themeToggle = document.getElementById('themeToggle');
const body = document.body;

themeToggle.addEventListener('click', () => {
  const currentTheme = body.getAttribute('data-theme') || 'dark';
  const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
  
  body.setAttribute('data-theme', newTheme);
  localStorage.setItem('theme', newTheme);
  
  // Update toggle icon
  const icon = themeToggle.querySelector('i');
  icon.className = newTheme === 'light' ? 'fas fa-moon' : 'fas fa-sun';
});</code></pre>
                        <p>This code toggles between light and dark themes and saves the preference in localStorage.</p>
                        <button class="copy-btn" title="Copy message"><i class="fas fa-copy"></i></button>
                        <div class="message-timestamp">Just now</div>
                    </div>
                </div>
            </div>
            
            <div class="typing-indicator" id="typingIndicator">
                <span>AI is typing</span>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
                <div class="typing-dot"></div>
            </div>
            
            <div class="chat-input-container">
                <div class="chat-input-box">
                    <textarea class="message-input" id="messageInput" placeholder="Type your message..." autofocus></textarea>
                    <button class="send-button" id="sendButton">
                        <i class="fas fa-paper-plane"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
// Theme toggle functionality
const themeToggle = document.getElementById('themeToggle');
const themeIcon = themeToggle.querySelector('i');
const body = document.body;

// Check for saved theme preference
const savedTheme = localStorage.getItem('theme');
if (savedTheme) {
    body.setAttribute('data-theme', savedTheme);
    themeIcon.className = savedTheme === 'light' ? 'fas fa-sun' : 'fas fa-moon';
}

// Toggle theme
themeToggle.addEventListener('click', () => {
    const currentTheme = body.getAttribute('data-theme') || 'dark';
    const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
    
    body.setAttribute('data-theme', newTheme);
    themeIcon.className = newTheme === 'light' ? 'fas fa-sun' : 'fas fa-moon';
    
    // Save theme preference
    localStorage.setItem('theme', newTheme);
});

// Model selection - sidebar
const modelOptions = document.querySelectorAll('.model-option');
const currentModel = document.querySelector('.current-model');

// Model selection - dropdown
const modelSelectBtn = document.getElementById('modelSelect');
const modelDropdown = document.getElementById('modelDropdown');
const modelOptionsDropdown = document.querySelectorAll('.model-option-dropdown');
const currentModelName = document.getElementById('currentModelName');

// Toggle model dropdown
modelSelectBtn.addEventListener('click', (e) => {
    modelDropdown.classList.toggle('active');
    e.stopPropagation();
});

// Close dropdown when clicking outside
document.addEventListener('click', (e) => {
    if (!modelSelectBtn.contains(e.target)) {
        modelDropdown.classList.remove('active');
    }
});

// Model selection functionality
function selectModel(option, isDropdown) {
    // Remove active class from all options
    modelOptions.forEach(opt => opt.classList.remove('active'));
    modelOptionsDropdown.forEach(opt => opt.classList.remove('active'));
    
    // Add active class to clicked option
    option.classList.add('active');
    
    // Update current model display
    const modelName = option.querySelector('span') ? 
        option.querySelector('span').textContent : 
        option.querySelector('.model-name').textContent;
        
    currentModel.querySelector('div:last-child').textContent = modelName;
    currentModelName.textContent = modelName;
    
    // Update dropdown if selection was from sidebar
    if (!isDropdown) {
        modelOptionsDropdown.forEach(opt => {
            if (opt.dataset.model === option.dataset.model) {
                opt.classList.add('active');
            }
        });
    }
    
    // Update sidebar if selection was from dropdown
    if (isDropdown) {
        modelOptions.forEach(opt => {
            if (opt.dataset.model === option.dataset.model) {
                opt.classList.add('active');
            }
        });
    }
    
    // Show notification
    showNotification(`Switched to ${modelName}`);
}

// Add event listeners to model options (sidebar)
modelOptions.forEach(option => {
    option.addEventListener('click', () => {
        selectModel(option, false);
    });
});

// Add event listeners to model options (dropdown)
modelOptionsDropdown.forEach(option => {
    option.addEventListener('click', () => {
        selectModel(option, true);
        modelDropdown.classList.remove('active');
    });
});

// Mobile sidebar toggle
const menuToggle = document.getElementById('menuToggle');
const closeSidebar = document.getElementById('closeSidebar');
const sidebar = document.getElementById('sidebar');

menuToggle.addEventListener('click', (e) => {
    sidebar.classList.toggle('active');
    e.stopPropagation(); // Prevent event from bubbling up
});

closeSidebar.addEventListener('click', () => {
    sidebar.classList.remove('active');
});

// Close sidebar when clicking outside
document.addEventListener('click', (e) => {
    if (sidebar.classList.contains('active') && 
        !sidebar.contains(e.target) && 
        e.target !== menuToggle) {
        sidebar.classList.remove('active');
    }
});

// Chat functionality
const chatMessages = document.getElementById('chatMessages');
const messageInput = document.getElementById('messageInput');
const sendButton = document.getElementById('sendButton');
const typingIndicator = document.getElementById('typingIndicator');

// Hide typing indicator initially
typingIndicator.style.display = 'none';

// Send message on button click
sendButton.addEventListener('click', sendMessage);

// Send message on Enter key (with Shift for new line)
messageInput.addEventListener('keydown', (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
        e.preventDefault();
        sendMessage();
    }
});

// Auto-resize textarea as user types
messageInput.addEventListener('input', function() {
    this.style.height = 'auto';
    this.style.height = (this.scrollHeight) + 'px';
});

function sendMessage() {
    const message = messageInput.value.trim();
    if (message) {
        // Add user message
        addMessage(message, 'user');
        
        // Clear input and reset height
        messageInput.value = '';
        messageInput.style.height = 'auto';
        
        // Show typing indicator
        typingIndicator.style.display = 'flex';
        
        // Simulate AI response after delay
        setTimeout(() => {
            typingIndicator.style.display = 'none';
            generateAIResponse(message);
        }, 1500);
    }
}

function addMessage(text, sender) {
    const messageDiv = document.createElement('div');
    messageDiv.classList.add('message');
    messageDiv.classList.add(sender + '-message');
    
    const avatar = document.createElement('div');
    avatar.classList.add('avatar');
    avatar.classList.add(sender + '-avatar');
    
    const icon = document.createElement('i');
    icon.className = sender === 'user' ? 'fas fa-user' : 'fas fa-robot';
    avatar.appendChild(icon);
    
    const contentDiv = document.createElement('div');
    contentDiv.classList.add('message-content');
    
    // Format paragraphs for multi-line messages
    const paragraphs = text.split('\n');
    paragraphs.forEach(para => {
        if (para.trim() !== '') {
            const p = document.createElement('p');
            p.textContent = para;
            contentDiv.appendChild(p);
        }
    });
    
    // Add copy button
    const copyBtn = document.createElement('button');
    copyBtn.classList.add('copy-btn');
    copyBtn.innerHTML = '<i class="fas fa-copy"></i>';
    copyBtn.title = 'Copy message';
    copyBtn.addEventListener('click', () => {
        // Copy message text to clipboard
        navigator.clipboard.writeText(text)
            .then(() => {
                showNotification('Message copied to clipboard');
            })
            .catch(err => {
                console.error('Could not copy text: ', err);
                showNotification('Failed to copy message');
            });
    });
    contentDiv.appendChild(copyBtn);
    
    // Document ready equivalent
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize textarea height
        messageInput.style.height = 'auto';
        messageInput.style.height = (messageInput.scrollHeight) + 'px';
        
        // Add event listeners to existing copy buttons
        document.querySelectorAll('.copy-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const messageContent = this.closest('.message-content');
                let textToCopy = '';
                
                // Get all paragraph text
                messageContent.querySelectorAll('p').forEach(p => {
                    textToCopy += p.textContent + '\n';
                });
                
                // Get code block if exists
                const codeBlock = messageContent.querySelector('pre code');
                if (codeBlock) {
                    textToCopy += codeBlock.textContent;
                }
                
                // Copy to clipboard
                navigator.clipboard.writeText(textToCopy.trim())
                    .then(() => {
                        showNotification('Message copied to clipboard');
                    })
                    .catch(err => {
                        console.error('Could not copy text: ', err);
                        showNotification('Failed to copy message');
                    });
            });
        });
    });
    
    const timestamp = document.createElement('div');
    timestamp.classList.add('message-timestamp');
    timestamp.textContent = 'Just now';
    contentDiv.appendChild(timestamp);
    
    messageDiv.appendChild(avatar);
    messageDiv.appendChild(contentDiv);
    
    chatMessages.appendChild(messageDiv);
    
    // Scroll to bottom
    chatMessages.scrollTop = chatMessages.scrollHeight;
}

function generateAIResponse(userMessage) {
    const responses = [
        `I understand you're asking about "${userMessage}". This is a complex topic that requires careful consideration. Could you provide more details about what specifically you'd like to know?`,
        `Interesting question about "${userMessage}"! Based on my knowledge, here are three key points to consider:\n\n1. First important aspect\n2. Second critical factor\n3. Third essential element\n\nWould you like me to expand on any of these?`,
        `Regarding "${userMessage}", I can provide several insights. First, it's important to note the context matters significantly. Second, current best practices suggest a multi-faceted approach. Finally, implementation details often depend on your specific use case.`,
        `"${userMessage}" - great topic! I've prepared a detailed response:\n\n• Analysis of the core concept\n• Comparison with alternative approaches\n• Practical implementation examples\n• Potential challenges and solutions\n\nLet me know which part you'd like to explore first.`,
        `I've considered your query about "${userMessage}" and here's my comprehensive response. The subject involves several dimensions: technical, practical, and theoretical. From a technical perspective...`
    ];
    
    const randomResponse = responses[Math.floor(Math.random() * responses.length)];
    addMessage(randomResponse, 'ai');
}

// New chat button
const newChatBtn = document.getElementById('newChatBtn');

newChatBtn.addEventListener('click', () => {
    // Clear chat messages
    chatMessages.innerHTML = '';
    
    // Add initial AI message
    addMessage("Hello! I'm your AI assistant. What would you like to discuss today? You can ask me anything - I'm here to help with your projects, answer questions, or brainstorm ideas.", 'ai');
    
    // Show notification
    showNotification('New chat started');
});

// Conversation selection
const conversationItems = document.querySelectorAll('.conversation-item');

conversationItems.forEach(item => {
    item.addEventListener('click', () => {
        conversationItems.forEach(i => i.classList.remove('active'));
        item.classList.add('active');
        
        // In a real app, this would load the conversation history
        showNotification(`Loaded conversation: ${item.textContent.trim()}`);
    });
});

// Show notification function
function showNotification(message) {
    // Create notification element
    const notification = document.createElement('div');
    notification.textContent = message;
    notification.style.position = 'fixed';
    notification.style.bottom = '20px';
    notification.style.right = '20px';
    notification.style.backgroundColor = 'var(--primary)';
    notification.style.color = 'white';
    notification.style.padding = '12px 20px';
    notification.style.borderRadius = '8px';
    notification.style.boxShadow = '0 4px 12px rgba(0,0,0,0.15)';
    notification.style.zIndex = '1000';
    notification.style.opacity = '0';
    notification.style.transform = 'translateY(20px)';
    notification.style.transition = 'all 0.3s ease';
    
    document.body.appendChild(notification);
    
    // Animate in
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateY(0)';
    }, 10);
    
    // Remove after delay
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateY(20px)';
        
        // Remove element after animation
        setTimeout(() => {
            notification.remove();
        }, 300);
    }, 3000);
}
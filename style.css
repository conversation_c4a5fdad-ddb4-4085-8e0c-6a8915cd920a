:root {
    --primary: #7E57C2;
    --primary-dark: #5E35B1;
    --secondary: #26A69A;
    --accent: #FFCA28;
    --dark: #263238;
    --light: #f5f7fa;
    --gray: #78909C;
    --light-gray: #ECEFF1;
    --card-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
    --transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.1);
    
    /* Theme-specific variables */
    --bg-gradient: linear-gradient(135deg, #2c1b47 0%, #1c2331 100%);
    --header-bg: rgba(38, 50, 56, 0.95);
    --card-bg: rgba(38, 50, 56, 0.8);
    --text-color: #f5f7fa;
    --text-secondary: #CFD8DC;
    --border-color: rgba(126, 87, 194, 0.3);
    --chat-bg: rgba(25, 32, 39, 0.9);
    --user-msg: rgba(126, 87, 194, 0.25);
    --ai-msg: rgba(38, 50, 56, 0.7);
}

/* Light Theme Overrides */
[data-theme="light"] {
    --bg-gradient: linear-gradient(135deg, #f0f2f5 0%, #e4e6e9 100%);
    --header-bg: rgba(255, 255, 255, 0.95);
    --card-bg: rgba(255, 255, 255, 0.9);
    --text-color: #263238;
    --text-secondary: #546E7A;
    --border-color: rgba(206, 212, 218, 0.5);
    --chat-bg: rgba(245, 247, 250, 0.95);
    --user-msg: rgba(126, 87, 194, 0.15);
    --ai-msg: rgba(255, 255, 255, 0.8);
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', 'Segoe UI', sans-serif;
}

body {
    background: var(--bg-gradient);
    min-height: 100vh;
    color: var(--text-color);
    transition: var(--transition);
    display: flex;
    flex-direction: column;
    height: 100vh;
    overflow: hidden;
}

/* Header */
.header {
    background: var(--header-bg);
    backdrop-filter: blur(10px);
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid var(--border-color);
    z-index: 100;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.header-left {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 1.3rem;
    font-weight: 700;
    color: var(--text-color);
    text-decoration: none;
}

.logo i {
    color: var(--accent);
    font-size: 1.5rem;
}

.header-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.theme-toggle {
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 1.2rem;
    cursor: pointer;
    transition: var(--transition);
    width: 38px;
    height: 38px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.theme-toggle:hover {
    background: rgba(126, 87, 194, 0.1);
    color: var(--accent);
}

.user-btn {
    background: transparent;
    border: 2px solid var(--primary);
    color: var(--text-color);
    padding: 6px 16px;
    border-radius: 30px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.user-btn:hover {
    background: var(--primary);
    color: white;
}

/* Model Select Dropdown */
.model-select {
    position: relative;
    display: inline-block;
}

.model-select-btn {
    background: rgba(126, 87, 194, 0.2);
    border: 1px solid var(--primary);
    color: var(--text-color);
    padding: 6px 12px;
    border-radius: 30px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: var(--transition);
    font-size: 0.9rem;
}

.model-select-btn:hover {
    background: rgba(126, 87, 194, 0.3);
}

.model-dropdown {
    position: absolute;
    top: 110%;
    right: 0;
    background: var(--header-bg);
    backdrop-filter: blur(10px);
    border-radius: 12px;
    box-shadow: var(--card-shadow);
    width: 220px;
    z-index: 200;
    overflow: hidden;
    display: none;
    border: 1px solid var(--border-color);
}

.model-dropdown.active {
    display: block;
}

.model-option-dropdown {
    padding: 10px 12px;
    display: flex;
    align-items: center;
    gap: 12px;
    cursor: pointer;
    transition: var(--transition);
}

.model-option-dropdown:hover {
    background: rgba(126, 87, 194, 0.2);
}

.model-option-dropdown.active {
    background: rgba(126, 87, 194, 0.25);
}

/* Main Layout */
.chat-container {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar */
.sidebar {
    width: 260px;
    background: var(--header-bg);
    backdrop-filter: blur(10px);
    border-right: 1px solid var(--border-color);
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    transition: var(--transition);
    overflow-y: auto;
}

.model-selector {
    padding: 0 20px 20px;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: 20px;
}

.model-selector h3 {
    font-size: 1rem;
    margin-bottom: 12px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.model-selector h3 i {
    color: var(--accent);
    font-size: 1.1rem;
}

.model-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.model-option {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 10px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    background: transparent;
    border: 1px solid var(--border-color);
}

.model-option:hover, .model-option.active {
    background: rgba(126, 87, 194, 0.2);
    border-color: var(--primary);
}

.model-option.active .model-icon {
    background: var(--primary);
    color: white;
}

.model-icon {
    width: 36px;
    height: 36px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    background: rgba(126, 87, 194, 0.2);
    color: var(--primary);
}

.model-info {
    flex: 1;
}

.model-name {
    font-weight: 600;
    font-size: 0.95rem;
}

.model-desc {
    font-size: 0.75rem;
    color: var(--text-secondary);
    margin-top: 2px;
}

.conversations {
    padding: 0 20px;
    flex: 1;
}

.conversations h3 {
    font-size: 1rem;
    margin-bottom: 12px;
    color: var(--text-secondary);
    display: flex;
    align-items: center;
    gap: 8px;
}

.conversations h3 i {
    color: var(--accent);
    font-size: 1.1rem;
}

.new-chat-btn {
    width: 100%;
    padding: 10px;
    background: var(--primary);
    color: white;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    margin-bottom: 15px;
    cursor: pointer;
    transition: var(--transition);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    font-size: 0.95rem;
}

.new-chat-btn:hover {
    background: var(--primary-dark);
    transform: translateY(-2px);
}

.conversation-list {
    list-style: none;
}

.conversation-item {
    padding: 10px 12px;
    border-radius: 8px;
    cursor: pointer;
    transition: var(--transition);
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    gap: 10px;
    font-size: 0.9rem;
}

.conversation-item:hover {
    background: rgba(126, 87, 194, 0.15);
}

.conversation-item i {
    color: var(--text-secondary);
    font-size: 0.9rem;
}

.conversation-item.active {
    background: rgba(126, 87, 194, 0.25);
    font-weight: 500;
}

/* Chat Area */
.chat-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: var(--chat-bg);
    backdrop-filter: blur(5px);
    width: 100%;
    overflow-x: hidden;
}

.chat-header {
    padding: 12px 20px;
    border-bottom: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.current-model {
    display: flex;
    align-items: center;
    gap: 8px;
    background: rgba(126, 87, 194, 0.2);
    padding: 6px 12px;
    border-radius: 30px;
    flex-shrink: 0;
    font-size: 0.95rem;
}

.model-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: var(--secondary);
}

.chat-messages {
    flex: 1;
    padding: 20px;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.message {
    display: flex;
    gap: 12px;
    max-width: 95%;
    animation: fadeIn 0.3s ease-in-out;
    width: 100%;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.user-message {
    align-self: flex-end;
    flex-direction: row-reverse;
}

.avatar {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    flex-shrink: 0;
}

.user-avatar {
    background: var(--primary);
    color: white;
}

.ai-avatar {
    background: var(--secondary);
    color: white;
}

.message-content {
    padding: 12px 16px;
    border-radius: 16px;
    line-height: 1.5;
    box-shadow: var(--card-shadow);
    word-wrap: break-word;
    overflow-wrap: anywhere;
    max-width: 100%;
    width: 100%;
    position: relative;
}

.copy-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(0, 0, 0, 0.1);
    border: none;
    border-radius: 4px;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    opacity: 0;
    transition: opacity 0.2s ease;
    color: var(--text-secondary);
}

.message-content:hover .copy-btn {
    opacity: 1;
}

.copy-btn:hover {
    background: rgba(126, 87, 194, 0.2);
    color: var(--primary);
}

.user-message .message-content {
    background: var(--user-msg);
    border-bottom-right-radius: 5px;
}

.ai-message .message-content {
    background: var(--ai-msg);
    border-bottom-left-radius: 5px;
}

.message-content p {
    margin-bottom: 8px;
    font-size: 0.95rem;
}

.message-content p:last-child {
    margin-bottom: 0;
}

.message-content code {
    background: rgba(0, 0, 0, 0.1);
    padding: 2px 5px;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.85em;
    word-break: break-word;
}

.message-content pre {
    background: rgba(0, 0, 0, 0.15);
    padding: 10px;
    border-radius: 8px;
    overflow-x: auto;
    margin: 8px 0;
    font-family: monospace;
    font-size: 0.85rem;
    max-width: 100%;
}

.message-timestamp {
    font-size: 0.7rem;
    color: var(--text-secondary);
    margin-top: 6px;
    text-align: right;
}

.chat-input-container {
    padding: 15px 20px;
    border-top: 1px solid var(--border-color);
    width: 100%;
}

.chat-input-box {
    display: flex;
    gap: 12px;
    background: var(--card-bg);
    border-radius: 16px;
    padding: 6px;
    border: 1px solid var(--border-color);
    align-items: flex-end;
    width: 100%;
    max-width: 100%;
}

.message-input {
    flex: 1;
    background: transparent;
    border: none;
    color: var(--text-color);
    padding: 10px 12px;
    font-size: 0.95rem;
    resize: none;
    min-height: 50px;
    max-height: 120px;
    outline: none;
    line-height: 1.5;
    word-wrap: break-word;
    overflow-wrap: anywhere;
}

.message-input::placeholder {
    color: var(--text-secondary);
}

.send-button {
    width: 46px;
    height: 46px;
    border-radius: 50%;
    background: var(--primary);
    color: white;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.1rem;
    cursor: pointer;
    transition: var(--transition);
    flex-shrink: 0;
    margin-bottom: 5px;
}

.send-button:hover {
    background: var(--primary-dark);
    transform: translateY(-3px);
}

.typing-indicator {
    display: flex;
    align-items: center;
    gap: 5px;
    color: var(--text-secondary);
    font-size: 0.85rem;
    padding: 0 12px;
    height: 18px;
}

.typing-dot {
    width: 6px;
    height: 6px;
    background: var(--text-secondary);
    border-radius: 50%;
    opacity: 0.6;
}

/* Close button for sidebar */
.close-sidebar {
    display: none;
    position: absolute;
    top: 12px;
    right: 15px;
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 1.4rem;
    cursor: pointer;
    z-index: 501;
}

/* Responsive Design */
@media (max-width: 900px) {
    .sidebar {
        position: fixed;
        left: -280px;
        height: 100%;
        top: 0;
        z-index: 1000;
        box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
        transition: left 0.3s ease;
    }
    
    .sidebar.active {
        left: 0;
        width: 280px;
    }
    
    .menu-toggle {
        display: block;
    }
    
    .message {
        max-width: 95%;
    }
    
    .close-sidebar {
        display: block;
    }
}

@media (max-width: 700px) {
    .header {
        padding: 10px 15px;
    }
    
    .logo {
        font-size: 1.2rem;
    }
    
    .logo i {
        font-size: 1.3rem;
    }
    
    .theme-toggle {
        width: 36px;
        height: 36px;
        font-size: 1.1rem;
    }
    
    .user-btn {
        padding: 5px 12px;
        font-size: 0.85rem;
    }
    
    .model-select-btn {
        padding: 5px 10px;
        font-size: 0.85rem;
    }
    
    .chat-messages {
        padding: 15px;
        gap: 15px;
    }
    
    .message-content {
        padding: 10px 14px;
        font-size: 0.9rem;
    }
    
    .message-content p {
        font-size: 0.9rem;
    }
    
    .message-content pre {
        padding: 8px;
        font-size: 0.8rem;
    }
    
    .avatar {
        width: 32px;
        height: 32px;
        font-size: 1rem;
    }
    
    .chat-input-container {
        padding: 12px 15px;
    }
    
    .message-input {
        padding: 8px 10px;
        font-size: 0.9rem;
    }
    
    .send-button {
        width: 42px;
        height: 42px;
        font-size: 1rem;
    }
}

@media (max-width: 500px) {
    .header {
        padding: 8px 12px;
    }
    
    .logo span {
        display: none;
    }
    
    .model-select-btn span {
        display: none;
    }
    
    .model-select-btn i:first-child {
        margin-right: 0;
    }
    
    .user-btn span {
        display: none;
    }
    
    .user-btn {
        width: 36px;
        height: 36px;
        padding: 0;
        justify-content: center;
    }
    
    .chat-header {
        padding: 8px 12px;
    }
    
    .current-model {
        font-size: 0.85rem;
    }
    
    .chat-messages {
        padding: 12px;
        gap: 12px;
    }
    
    .message {
        max-width: 100%;
        gap: 10px;
        width: 100%;
    }
    
    .message-content {
        padding: 8px 12px;
        font-size: 0.85rem;
        width: calc(100% - 40px);
    }
    
    .message-content p {
        font-size: 0.85rem;
    }
    
    .message-content pre {
        padding: 6px;
        font-size: 0.75rem;
        max-width: 100%;
        overflow-x: auto;
    }
    
    .avatar {
        width: 30px;
        height: 30px;
        font-size: 0.9rem;
    }
    
    .chat-input-container {
        padding: 10px 12px;
        width: 100%;
    }
    
    .chat-input-box {
        width: 100%;
    }
    
    .message-input {
        padding: 6px 8px;
        font-size: 0.85rem;
        min-height: 45px;
        width: calc(100% - 50px);
    }
    
    .send-button {
        width: 38px;
        height: 38px;
        font-size: 0.9rem;
        flex-shrink: 0;
    }
    
    .copy-btn {
        width: 24px;
        height: 24px;
        top: 5px;
        right: 5px;
    }
}

@media (max-width: 400px) {
    .header {
        padding: 6px 10px;
    }
    
    .theme-toggle {
        width: 34px;
        height: 34px;
        font-size: 1rem;
    }
    
    .user-btn {
        width: 34px;
        height: 34px;
    }
    
    .model-select-btn {
        min-width: 34px;
        height: 34px;
        padding: 4px;
    }
    
    .chat-messages {
        padding: 10px;
    }
    
    .message-content {
        padding: 7px 10px;
        font-size: 0.82rem;
    }
    
    .message-content p {
        font-size: 0.82rem;
    }
    
    .message-content pre {
        padding: 5px;
        font-size: 0.72rem;
    }
    
    .message-timestamp {
        font-size: 0.65rem;
    }
    
    .chat-input-container {
        padding: 8px 10px;
    }
    
    .message-input {
        padding: 5px 7px;
        font-size: 0.82rem;
    }
    
    .send-button {
        width: 36px;
        height: 36px;
    }
    
    .typing-indicator {
        font-size: 0.8rem;
    }
}

@media (max-width: 350px) {
    .header {
        padding: 5px 8px;
    }
    
    .logo i {
        font-size: 1.1rem;
    }
    
    .theme-toggle {
        width: 32px;
        height: 32px;
        font-size: 0.95rem;
    }
    
    .model-select-btn {
        min-width: 32px;
        height: 32px;
    }
    
    .user-btn {
        width: 32px;
        height: 32px;
    }
    
    .chat-header {
        padding: 6px 8px;
    }
    
    .current-model {
        padding: 5px 10px;
        font-size: 0.8rem;
    }
    
    .chat-messages {
        padding: 8px;
    }
    
    .message-content {
        padding: 6px 8px;
        font-size: 0.8rem;
    }
    
    .message-content p {
        font-size: 0.8rem;
    }
    
    .message-content pre {
        padding: 4px;
        font-size: 0.7rem;
    }
    
    .avatar {
        width: 28px;
        height: 28px;
        font-size: 0.85rem;
    }
    
    .chat-input-container {
        padding: 6px 8px;
    }
    
    .message-input {
        padding: 4px 6px;
        font-size: 0.8rem;
        min-height: 40px;
    }
    
    .send-button {
        width: 34px;
        height: 34px;
        font-size: 0.85rem;
    }
    
    .typing-indicator {
        font-size: 0.75rem;
    }
    
    .conversation-item {
        padding: 8px 10px;
        font-size: 0.85rem;
    }
    
    .new-chat-btn {
        font-size: 0.9rem;
        padding: 8px;
    }
}

/* Toggle sidebar button for mobile */
.menu-toggle {
    display: none;
    background: transparent;
    border: none;
    color: var(--text-color);
    font-size: 1.4rem;
    cursor: pointer;
    margin-right: 12px;
}

@media (max-width: 900px) {
    .menu-toggle {
        display: block;
    }
}

/* Scrollbar styling */
::-webkit-scrollbar {
    width: 6px;
}

::-webkit-scrollbar-track {
    background: rgba(0, 0, 0, 0.1);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb {
    background: var(--primary);
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-dark);
}